package me.simplicitee.projectaddons.ability.water;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.util.EulerAngle;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.ComboAbility;
import com.projectkorra.projectkorra.ability.PlantAbility;
import com.projectkorra.projectkorra.ability.util.ComboManager.AbilityInformation;
import com.projectkorra.projectkorra.util.ClickType;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.StatisticsMethods;

import me.simplicitee.projectaddons.ProjectAddons;
import me.simplicitee.projectaddons.TLBPAMethods;

public class LeafStorm extends PlantAbility implements ComboAbility, AddonAbility {
	private double radius;
	private double damage;
	private int leaves;
	private long cooldown;
	private long duration;
	private double angleSpeed;
	private int maxHits;
	private int hitCount;

	private final Set<Leaf> leafTracker = new HashSet<>();
	
	public LeafStorm(Player player) {
		super(player);
		
		if (bPlayer.isOnCooldown("LeafStorm")) {
			remove();
			return;
		}
		
		setFields();
		for (int i = 0; i < leaves; i++) {
			Location loc = player.getEyeLocation().clone();
			loc.add(0, Math.random() * 2 - 2, 0); // Lower spawn level
			
			// Distribute leaves evenly around the player
			double angle = (360.0 / leaves) * i + (Math.random() * 30 - 10); // Even distribution with slight randomness
			double offset = Math.random() * (radius/5) + (radius/8); // Randomize distance but keep within radius
			
			leafTracker.add(new Leaf(loc, angle, offset));
		}
		start();
	}

  private void setFields() {
    int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
    long currentLevel = TLBMethods.limitLevels(player, statLevel);

    radius = TLBPAMethods.getDouble("Combos.LeafStorm.Radius", currentLevel);
    damage = TLBPAMethods.getDouble("Combos.LeafStorm.Damage", currentLevel);
    leaves = TLBPAMethods.getInt("Combos.LeafStorm.LeafCount", currentLevel);
    cooldown = TLBPAMethods.getLong("Combos.LeafStorm.Cooldown", currentLevel);
    duration = TLBPAMethods.getLong("Combos.LeafStorm.Duration", currentLevel);
    angleSpeed = TLBPAMethods.getDouble("Combos.LeafStorm.LeafSpeed", currentLevel);
    maxHits = TLBPAMethods.getInt("Combos.LeafStorm.MaxHits", currentLevel);
    hitCount = 0;
  }

	@Override
	public void progress() {
		if (!player.isOnline() || player.isDead()) {
			bPlayer.addCooldown("LeafStorm", cooldown);
			remove();
			return;
		}
		
		if (!player.isSneaking() || !bPlayer.canBendIgnoreBinds(getAbility("LeafStorm"))) {
			bPlayer.addCooldown("LeafStorm", cooldown);
			remove();
			return;
		}

		// Check if duration has expired (if duration > 0)
		if (duration > 0 && System.currentTimeMillis() > getStartTime() + duration) {
			bPlayer.addCooldown("LeafStorm", cooldown);
			remove();
			return;
		}

		// Check if max hits reached
		if (hitCount >= maxHits) {
			bPlayer.addCooldown("LeafStorm", cooldown);
			remove();
			return;
		}
		
		
		Set<Leaf> removal = new HashSet<>();
		leaves: for (Leaf leaf : leafTracker) {
			leaf.update();
			
			if (!leaf.getLocation().getBlock().isPassable()) {
				removal.add(leaf);
				continue;
			}
			
			ArmorStand stand = (ArmorStand) player.getWorld().spawnEntity(leaf.getLocation(), EntityType.ARMOR_STAND);
			stand.setVisible(false);
			stand.setGravity(false);
			stand.setSmall(true);
			ItemStack kelpItem = new ItemStack(Material.KELP);
			stand.setHelmet(kelpItem);
			
			// Randomize rotation
			stand.setHeadPose(new EulerAngle(
				Math.toRadians(Math.random() * 360),
				Math.toRadians(Math.random() * 360),
				Math.toRadians(Math.random() * 360)
			));
			
			// Add block dust particles for fullness
			leaf.getLocation().getWorld().spawnParticle(
				Particle.BLOCK, 
				leaf.getLocation(), 
				3, 
				0.1, 0.1, 0.1, 
				0.01, 
				Material.OAK_LEAVES.createBlockData()
			);
			
			// Remove the stand after a short delay
			Bukkit.getScheduler().runTaskLater(ProjectAddons.instance, stand::remove, 5L);
			
			for (Entity e : GeneralMethods.getEntitiesAroundPoint(leaf.getLocation(), 0.5)) {
				if (e instanceof LivingEntity && e.getEntityId() != player.getEntityId()) {
					DamageHandler.damageEntity(e, damage, this);
					hitCount++;
					removal.add(leaf);
					continue leaves;
				}
			}
		}
		
		leafTracker.removeAll(removal);
		
		
		if (leafTracker.isEmpty()) {
			bPlayer.addCooldown("LeafStorm", cooldown);
			remove();
			return;
		}
	}
	
	@Override
	public void remove() {
		super.remove();
	}

	@Override
	public boolean isSneakAbility() {
		return true;
	}

	@Override
	public boolean isHarmlessAbility() {
		return false;
	}

	@Override
	public long getCooldown() {
		return cooldown;
	}

	@Override
	public String getName() {
		return "LeafStorm";
	}

	@Override
	public Location getLocation() {
		return null;
	}

	@Override
	public void load() {}

	@Override
	public void stop() {}

	@Override
	public String getAuthor() {
		return "Simplicitee";
	}

	@Override
	public String getVersion() {
		return ProjectAddons.instance.version();
	}

	@Override
	public Object createNewComboInstance(Player player) {
		return new LeafStorm(player);
	}

	@Override
	public ArrayList<AbilityInformation> getCombination() {
		ArrayList<AbilityInformation> combo = new ArrayList<>();
		
		combo.add(new AbilityInformation("RazorLeaf", ClickType.LEFT_CLICK));
		combo.add(new AbilityInformation("RazorLeaf", ClickType.LEFT_CLICK));
		combo.add(new AbilityInformation("RootSpear", ClickType.SHIFT_DOWN));
		
		return combo;
	}
	
	@Override
	public boolean isEnabled() {
		return ProjectAddons.instance.getConfig().getBoolean("Combos.LeafStorm.Enabled");
	}
	
	@Override
	public String getDescription() {
		return "A combo that creates a whirling storm of leaves around you! Leaves disappear after hitting a block or entity, when the duration expires, or you stop sneaking.";
	}
	
	@Override
	public String getInstructions() {
		return "RazorLeaf (Double Click) > RootSpear (Hold Sneak)";
	}

	public class Leaf {
		
		private Location loc;
		private double angle, radius;
		
		private Leaf(Location loc, double angle, double radius) {
			this.loc = loc;
			this.angle = angle;
			this.radius = radius;
		}
		
		public Location getLocation() {
			return loc;
		}
		
		public double getAngle() {
			return angle;
		}
		
		public Leaf update() {
			Location loc = player.getEyeLocation().clone();
			double x = radius * Math.cos(Math.toRadians(angle));
			double z = radius * Math.sin(Math.toRadians(angle));
			
			loc.add(x, 0, z);
			loc.setY(this.loc.getY());
			
			this.loc = loc;
			angle += angleSpeed;
			
			return this;
		}
	}
}
